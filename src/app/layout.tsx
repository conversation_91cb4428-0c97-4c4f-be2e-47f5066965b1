import Footer from '@/components/Footer';
import Header from '@/components/Header';
import { Toaster } from "@/components/ui/toaster";
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

const inter = Inter({ subsets: ['latin'], variable: '--font-inter' });

export const metadata: Metadata = {
  title: 'Backro: Suas ferramentas para campanhas',
  description: 'Produto web para acelerar briefing, estratégia, mídia e relatórios.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pt-BR" className={`${inter.variable} dark`} suppressHydrationWarning>
      <body className="font-body bg-background text-foreground antialiased">

        <div className="relative flex size-full min-h-screen flex-col bg-[#111a22] dark group/design-root overflow-x-hidden" style={{ fontFamily: '<PERSON>, "Noto Sans", sans-serif' }}>
          <div className="layout-container flex h-full grow flex-col">
            <Header />
            <div className="px-4 sm:px-8 md:px-16 lg:px-24 xl:px-40 2xl:px-60 flex flex-1 justify-center py-5">
              {children}
            </div>
            <Footer />
          </div>
        </div>
        <Toaster />
      </body>
    </html>
  );
}
