'use client';

import CategoryFilter from '@/components/CategoryFilter';
import {
  BrainIcon,
  CalendarIcon,
  ChartLineIcon,
  ClipboardIcon,
  ImageIcon,
  MagicWandIcon,
  MegaphoneIcon,
  PencilIcon,
  RobotIcon,
  ShareIcon,
  TargetIcon,
  VideoIcon
} from '@/components/Icons';
import ToolCard from '@/components/ToolCard';
import { useState } from 'react';

export default function ToolsScreen() {
  const [activeCategory, setActiveCategory] = useState('Todas');
  const [searchText, setSearchText] = useState('');
  const [selectedCampaign, setSelectedCampaign] = useState('');
  const [showMvpOnly, setShowMvpOnly] = useState(false);

  const categories = [
    'Todas',
    'Atendimento',
    'Mídia',
    'Criação',
    'Produção',
    'Publicação',
    'Social Media',
    'BI/Monitoramento'
  ];

  const campaigns: string[] = [
    // Por enquanto não temos campanhas
  ];

  const tools = [
    // ÁREA - Atendimento
    {
      id: 1,
      icon: <ClipboardIcon size={32} />,
      title: 'Briefing Inicial',
      description: 'Ferramenta inteligente para capturar e organizar informações essenciais do cliente de forma estruturada.',
      category: 'Atendimento',
      features: [
        'Questionário inteligente',
        'Análise de necessidades',
        'Documentação automática',
        'Integração com CRM'
    ],
      isMvp: true,
      isPopular: true
    },

    // ÁREA - Mídia
    {
      id: 2,
      icon: <TargetIcon size={32} />,
      title: 'Plano de Mídia',
      description: 'Crie planos de mídia estratégicos com base em dados de audiência e performance.',
      category: 'Mídia',
      features: [
        'Análise de audiência',
        'Otimização de budget',
        'Seleção de canais',
        'Cronograma automático'
      ],
      isMvp: true
    },

    // ÁREA - Criação
    {
      id: 3,
      icon: <MagicWandIcon size={32} />,
      title: 'Conceito Criativo e KV',
      description: 'Desenvolva conceitos criativos e direcionamentos de Key Visual com IA avançada.',
      category: 'Criação',
      features: [
        'Geração de conceitos',
        'Direcionamento visual',
        'Análise de tendências',
        'Referências criativas'
      ],
      isMvp: true,
      isPopular: true
    },
    {
      id: 4,
      icon: <PencilIcon size={32} />,
      title: 'Gerador de Textos',
      description: 'Crie textos persuasivos e envolventes para suas campanhas publicitárias.',
      category: 'Criação',
      features: [
        'Textos para anúncios',
        'Descrições de produtos',
        'Posts para redes sociais',
        'E-mails marketing'
      ]
    },

    // ÁREA - Produção
    {
      id: 5,
      icon: <ImageIcon size={32} />,
      title: 'Produção - Peça Visual Final',
      description: 'Finalize peças visuais com qualidade profissional usando IA para otimização.',
      category: 'Produção',
      features: [
        'Edição automática',
        'Otimização de qualidade',
        'Múltiplos formatos',
        'Aprovação integrada'
      ],
      isMvp: true
    },
    {
      id: 6,
      icon: <VideoIcon size={32} />,
      title: 'Criador de Vídeos',
      description: 'Produza vídeos profissionais para suas campanhas com templates e IA.',
      category: 'Produção',
      features: [
        'Templates profissionais',
        'Edição automática',
        'Legendas automáticas',
        'Múltiplos formatos'
      ]
    },

    // ÁREA - Publicação
    {
      id: 7,
      icon: <ShareIcon size={32} />,
      title: 'Publicação/Acompanhamento',
      description: 'Publique e monitore suas campanhas em múltiplas plataformas simultaneamente.',
      category: 'Publicação',
      features: [
        'Publicação automática',
        'Agendamento inteligente',
        'Monitoramento em tempo real',
        'Relatórios de performance'
      ],
      isMvp: true
    },
    {
      id: 8,
      icon: <MegaphoneIcon size={32} />,
      title: 'Otimizador de Anúncios',
      description: 'Otimize automaticamente seus anúncios para maximizar conversões e ROI.',
      category: 'Publicação',
      features: [
        'Otimização automática',
        'A/B testing',
        'Segmentação inteligente',
        'Bid management'
      ]
    },

    // ÁREA - Social Media
    {
      id: 9,
      icon: <CalendarIcon size={32} />,
      title: 'Calendário Editorial',
      description: 'Organize e planeje seu conteúdo de redes sociais com calendário inteligente.',
      category: 'Social Media',
      features: [
        'Planejamento de conteúdo',
        'Sugestões de posts',
        'Análise de engajamento',
        'Agendamento automático'
      ]
    },
    {
      id: 10,
      icon: <RobotIcon size={32} />,
      title: 'Chatbot Inteligente',
      description: 'Automatize o atendimento ao cliente com chatbots personalizados.',
      category: 'Social Media',
      features: [
        'Respostas automáticas',
        'Integração com redes sociais',
        'Múltiplos canais',
        'Aprendizado contínuo'
      ]
    },

    // ÁREA - BI/Monitoramento
    {
      id: 11,
      icon: <ChartLineIcon size={32} />,
      title: 'Setup de Tracking',
      description: 'Configure sistemas de rastreamento e análise para monitorar performance.',
      category: 'BI/Monitoramento',
      features: [
        'Configuração automática',
        'Múltiplas plataformas',
        'Dashboards personalizados',
        'Alertas inteligentes'
      ]
    },
    {
      id: 12,
      icon: <BrainIcon size={32} />,
      title: 'Análise de Performance',
      description: 'Monitore e analise o desempenho de suas campanhas com insights de IA.',
      category: 'BI/Monitoramento',
      features: [
        'Métricas em tempo real',
        'Relatórios automáticos',
        'Insights de IA',
        'Comparação de campanhas'
      ]
    }
  ];

  const filteredTools = tools.filter(tool => {
    // Filtro por categoria
    const categoryMatch = activeCategory === 'Todas' || tool.category === activeCategory;

    // Filtro por texto de busca
    const searchMatch = searchText === '' ||
      tool.title.toLowerCase().includes(searchText.toLowerCase()) ||
      tool.description.toLowerCase().includes(searchText.toLowerCase()) ||
      tool.features.some(feature => feature.toLowerCase().includes(searchText.toLowerCase()));

    // Filtro por campanha (por enquanto não implementado)
    const campaignMatch = selectedCampaign === '' || true; // Sempre true por enquanto

    // Filtro MVP
    const mvpMatch = !showMvpOnly || tool.isMvp;

    return categoryMatch && searchMatch && campaignMatch && mvpMatch;
  });

  return (
    <div className="layout-content-container flex flex-col max-w-[960px] @[1440px]:max-w-[1200px] flex-1">
      {/* Header Section */}
      <div className="flex flex-col gap-4 px-4 py-10 @container">
        <div className="flex flex-col gap-2 text-center">
          <h1 className="text-white tracking-light text-[32px] font-bold leading-tight @[480px]:text-4xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em] max-w-[720px]">
            Ferramentas de IA para Agências
          </h1>
          <p className="text-white text-base font-normal leading-normal max-w-[720px]">
            Descubra nossa coleção completa de ferramentas alimentadas por IA,
            projetadas especificamente para potencializar suas campanhas publicitárias.
          </p>
        </div>
      </div>

      {/* Category Filter */}
      <CategoryFilter
        categories={categories}
        activeCategory={activeCategory}
        onCategoryChange={setActiveCategory}
        searchText={searchText}
        onSearchChange={setSearchText}
        selectedCampaign={selectedCampaign}
        onCampaignChange={setSelectedCampaign}
        showMvpOnly={showMvpOnly}
        onMvpToggle={setShowMvpOnly}
        campaigns={campaigns}
      />

      {/* Tools Grid */}
      <div className="grid grid-cols-1 @[480px]:grid-cols-2 @[768px]:grid-cols-3 @[1024px]:grid-cols-4 @[1440px]:grid-cols-5 gap-4 px-4 py-6">
        {filteredTools.map((tool) => (
          <ToolCard
            key={tool.id}
            icon={tool.icon}
            title={tool.title}
            description={tool.description}
            category={tool.category}
            features={tool.features}
            isPopular={tool.isPopular}
            isMvp={tool.isMvp}
          />
        ))}
      </div>

      {/* CTA Section */}
      <div className="@container">
        <div className="flex flex-col justify-end gap-6 px-4 py-10 @[480px]:gap-8 @[480px]:px-10 @[480px]:py-20">
          <div className="flex flex-col gap-2 text-center">
            <h2 className="text-white tracking-light text-[28px] font-bold leading-tight @[480px]:text-3xl @[480px]:font-black @[480px]:leading-tight @[480px]:tracking-[-0.033em] max-w-[720px]">
              Pronto para Revolucionar suas Campanhas?
            </h2>
            <p className="text-[#92adc9] text-base font-normal leading-normal max-w-[720px]">
              Comece a usar nossas ferramentas hoje mesmo e veja a diferença que a IA pode fazer.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
